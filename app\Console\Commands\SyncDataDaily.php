<?php

namespace App\Console\Commands;

use App\Models\DocumentRecord;
use App\Models\Library;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Illuminate\Console\Command;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SyncDataDaily extends Command
{
    protected $signature = 'sync:books';

    protected $description = 'Sync data from multiple APIs daily at midnight';

    private function generateBaseApiUrlsFromLibrariesTable()
    {

        $baseApiUrls = [];
        $libraries = Library::where('status', 'open')->get();

        foreach ($libraries as $library) {

            $libraryId = $library->id;
            $libraryIp = $library->ip_adress;
            $port = $library->api_port ?? '80';
            if (filter_var($libraryIp, FILTER_VALIDATE_IP)) {
                $baseApiUrl = "http://$libraryIp:$port/api";
            }

            $baseApiUrls[$libraryId] = $baseApiUrl;
        }

        return $baseApiUrls;
    }

    public function handle()
    {
        $baseApiUrls = [
            //34 => "http://************:56578/api",
            28 => "http://*************:45688/api",
        ];
        //dd($baseApiUrls);
        //dd($this->generateBaseApiUrlsFromLibrariesTable());

        $limit = 10;
        $scraped_records = 0;
        $batchSize = 10;

        foreach ($baseApiUrls as $key => $base_url) {
            $books_url = $base_url.'/books';
            // dd($books_url);
            if ($this->isApiAvailable($books_url)) {
                $response = Http::get($books_url, ['limit' => $limit]);
                $books = json_decode($response->body());
                $totalPages = $books->meta->last_page;

                $library_code = Library::where('code', $books->bplp_info->library_code)->first()->id;
                // dd($books->bplp_info->library_code,$library_code);
                $libraryTotalDocs = DocumentRecord::where('library_id', $library_code)->count();
                // dd($libraryTotalDocs);
                $totaldocs = $books->meta->total;

                // TODO: test if JSON is valid Format as expected

                $maxCreatedAt = '';
                $maxUpdatedAt = '';
                $this->output->text($totaldocs.' != '.$libraryTotalDocs);

                if ($libraryTotalDocs > 100000000000) {
                    if ($totaldocs > $libraryTotalDocs) {
                        // TODO: check if records count from database is the same on api
                        $maxCreatedAt = DocumentRecord::where('library_id', $library_code)->max('created_at');
                        $maxCreatedAt = Carbon::parse($maxCreatedAt)->toDateTimeString();
                        $response = Http::get($books_url.'?limit='.$limit.'&created_at='.$maxCreatedAt);
                        $totalPages = json_decode($response->body())->meta->last_page;
                        // dd($totalPages, $books_url . '?limit=' . $limit . '&created_at=' . $maxCreatedAt);
                        $this->output->text($totalPages, $books_url.'?limit='.$limit.'&created_at='.$maxCreatedAt);
                        if ($totalPages == 1) {
                            // DocumentRecord::where('library_id', $library_code)->delete();
                        }
                    }
                    if ($totaldocs == $libraryTotalDocs) {
                        // TODO: Try to get only updated from Api by checking updated_at from Api and compare it to max(updated_at)
                        $maxUpdatedAt = DocumentRecord::where('library_id', $library_code)->max('updated_at');
                        $maxUpdatedAt = Carbon::parse($maxUpdatedAt)->toDateTimeString();
                        $response = Http::get($books_url.'?limit='.$limit.'&updated_at='.$maxUpdatedAt);
                        $totalPages = json_decode($response->body())->meta->last_page;
                        // dd($books_url.'?limit='.$limit.'&created_at='.$maxCreatedAt);
                    }
                }
                $this->output->progressStart(($totalPages / $batchSize) * $limit);

                // TODO: check if last updated at and created at is the same
                // TODO : Add  a way to save last page stoped byany error and start from it next time
                // $page = 1390;
                $lastProcessedPage = 0;
                $lastProcessedPage = intval($libraryTotalDocs / $batchSize);

                for ($page = $lastProcessedPage; $page <= $totalPages; $page += $batchSize) {

                    // Use Http::pool for parallel requests
                    $responses = Http::pool(function (Pool $pool) use ($books_url, $limit, $totalPages, $maxCreatedAt, $maxUpdatedAt, $batchSize, $page) {
                        return collect()
                            ->range($page, min($page + $batchSize - 1, $totalPages))
                            ->map(function ($currentPage) use ($pool, $books_url, $limit, $maxCreatedAt, $maxUpdatedAt) {
                                if ($maxCreatedAt) {
                                    Log::info($books_url.'?limit='.$limit.'&page='.$currentPage.'&created_at='.$maxCreatedAt);

                                    return $pool->get($books_url.'?limit='.$limit.'&page='.$currentPage.'&created_at='.$maxCreatedAt);
                                }
                                if ($maxUpdatedAt) {
                                    Log::info($books_url.'?limit='.$limit.'&page='.$currentPage.'&created_at='.$maxCreatedAt);

                                    return $pool->get($books_url.'?limit='.$limit.'&page='.$currentPage.'&updated_at='.$maxUpdatedAt);
                                }
                                Log::info($books_url.'?limit='.$limit.'&page='.$currentPage);

                                return $pool->get($books_url.'?limit='.$limit.'&page='.$currentPage);
                            });
                    });
                    // Log::info('This is the responses');
                    // Log::info($responses);
                    foreach ($responses as $response) {
                        $lastpagebnonfail = $page;
                        try {
                            // Check if $response is an exception
                            if ($response instanceof \Exception) {
                                throw $response;
                            }

                            if ($response->successful()) {
                                // If the request was successful, process the response
                                $bookData = json_decode($response->getBody());

                                foreach ($bookData->data as $data) {
                                    $this->updateOrCreateDocumentRecord($data, $library_code);
                                    $scraped_records++;
                                }
                                $this->output->progressAdvance();
                            } else {
                                // Handle unsuccessful response
                                if ($response->status() === 429) {
                                    // Retry logic for 429 status code
                                    $retryCount = 0;
                                    $maxRetries = 5;
                                    $waitTime = 1; // Start with 1 second

                                    // Store the current page number for retry logic
                                    $currentPage = $page; // Ensure $currentPage is defined

                                    while ($retryCount < $maxRetries) {
                                        Log::warning("Received 429 status. Retrying in {$waitTime} seconds...");
                                        sleep($waitTime); // Wait before retrying
                                        $response = Http::get($books_url.'?limit='.$limit.'&page='.$currentPage); // Use the defined $currentPage

                                        if ($response->successful()) {
                                            // If the request was successful, process the response
                                            $bookData = json_decode($response->getBody());
                                            foreach ($bookData->data as $data) {
                                                $this->updateOrCreateDocumentRecord($data, $library_code);
                                                $scraped_records++;
                                            }
                                            $this->output->progressAdvance();
                                            break; // Exit the retry loop
                                        } elseif ($response->status() === 429) {
                                            $retryCount++;
                                            $waitTime *= 2; // Exponential backoff
                                        } else {
                                            throw new \Exception("Error fetching data from API. Status Code: {$response->status()}");
                                        }
                                    }

                                    if ($retryCount === $maxRetries) {
                                        Log::error('Max retries reached for API request. Skipping this request.');
                                    }
                                } else {
                                    throw new \Exception("Error fetching data from API. Status Code: {$response->status()}");
                                }
                            }
                        } catch (\Exception $e) {
                            Log::error("An error occurred while processing page {$page}: ".$e->getMessage());

                            // You might want to add retry logic here or handle specific exceptions differently
                            continue; // Skip to next response
                        }
                    }
                }
                $this->output->progressFinish();
            } else {
                // TODO: Send notification To bplp email
                // TODO: Send notification to admin with database notifications
                Notification::make()
                    ->title('Library Skipped')
                    ->info()
                    ->body("API at {$base_url} is not available. Skipping to the next API.")
                    ->send();
                // TODO: Update the bplp status to Closed in database
                Log::warning("API at {$base_url} is not available. Skipping to the next API.");
            }
        }
        // Log the result or send notifications if needed
        Notification::make()
            ->title('Synced data from multiple Libraries APIs')
            ->success()
            ->body('Synced data from multiple APIs. Total records: '.$scraped_records)
            ->send();
        Log::info('Synced data from multiple APIs. Total records: '.$scraped_records);
    }

    private function isApiAvailable($url)
    {
        try {
            $response = Http::get($url);

            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }

    private function updateOrCreateDocumentRecord($data, $library_code)
    {
        // dd($data, $library_code);

        DocumentRecord::updateOrCreate(
            // conditions
            [
                'library_id' => $library_code,
                'document_rid' => $data->id,
            ],
            // inserting fields
            [
                'library_id' => $library_code,
                'document_rid' => $data->id,
                'document_code' => $data->document_code,
                'document_title' => $data->document_title,
                'additional_title' => $data->additional_title,
                'parallel_title' => $data->parallel_title,
                'collection_title' => $data->collection_title,
                'collection_number' => $data->collection_number,
                'publication_location' => $data->publication_location ?? '',
                'classification_code' => $data->classification_code,
                'classification_name' => $data->classification_name,
                'publication_year' => $data->publication_year,
                'status' => $data->status,
                'document_type' => $data->document_type_name,
                'illustration' => $data->illustration,
                'isbn' => $data->isbn,
                'issn' => $data->issn,
                'other_number' => $data->other_number,
                'note' => $data->note,
                'keywords' => $data->keywords,
                'country' => $data->country_id ?? '',
                'language' => $data->langue,

                'collection' => $data->collection_id ?? '',

                'updated_at' => $data->updated_at,
                'created_at' => $data->created_at,

                'number_copies' => $data->number_copies,
                'copies' => json_encode($data->copies),
                'authors' => json_encode($data->authors),
                'editors' => json_encode($data->editors),
                // 'subject_headings' => json_encode($data->subject_headings),
            ]
        );
    }
}
