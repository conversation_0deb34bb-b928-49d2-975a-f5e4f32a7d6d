<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LibraryStatsResource\Pages;
use App\Models\Library;
use App\Models\LibraryStats;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class LibraryStatsResource extends Resource
{
    protected static ?string $model = LibraryStats::class;

    protected static ?string $navigationIcon = 'heroicon-o-presentation-chart-line';

    public static function getNavigationLabel(): string
    {
        return __('cruds.librarystats.title');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('cruds.libraryNetwork.title');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('library.name')
                    ->label(__('cruds.librarystats.fields.code'))
                    ->badge()
                    ->searchable(),
                TextColumn::make('total_titles')
                    ->label(__('cruds.librarystats.fields.total_titles'))
                    ->icon('heroicon-o-book-open')
                    ->numeric(
                        decimalPlaces: 0,
                        thousandsSeparator: ',',
                    ),
                TextColumn::make('total_copies')
                    ->label(__('cruds.librarystats.fields.total_copies'))
                    ->icon('heroicon-o-document-duplicate')
                    ->numeric(
                        decimalPlaces: 0,
                        thousandsSeparator: ',',
                    ),
                TextColumn::make('total_readers')
                    ->label(__('cruds.librarystats.fields.total_readers'))
                    ->icon('heroicon-o-users')
                    ->numeric(
                        decimalPlaces: 0,
                        thousandsSeparator: ',',
                    ),
                TextColumn::make('total_readers_female')
                    ->label(__('cruds.librarystats.fields.total_readers_female'))
                    ->icon('heroicon-o-user')
                    ->numeric(
                        decimalPlaces: 0,
                        thousandsSeparator: ',',
                    ),
                TextColumn::make('total_readers_male')
                    ->label(__('cruds.librarystats.fields.total_readers_male'))
                    ->icon('heroicon-o-user')
                    ->numeric(
                        decimalPlaces: 0,
                        thousandsSeparator: ',',
                    ),
                TextColumn::make('total_copies_rfid_tagged')
                    ->label(__('cruds.librarystats.fields.total_copies_rfid_tagged'))
                    ->icon('heroicon-o-tag')
                    ->numeric(
                        decimalPlaces: 0,
                        thousandsSeparator: ',',
                    ),
                // Tables\Columns\TextColumn::make('other_stats')
                //     ->label(__('cruds.librarystats.fields.other_stats'))
                //     ->icon('heroicon-o-clipboard-document-list'),
                //
            ])
            ->filters([
                SelectFilter::make('library_id')
                    ->label(__('cruds.documentRecord.fields.library'))
                    ->preload()
                    ->options(Library::where('status', 'Open')->pluck('name', 'id'))
                    ->searchable(),

            ], layout: FiltersLayout::AboveContent)->filtersTriggerAction(
                fn (Action $action) => $action
                    ->button()
                    ->label(__('global.filter')),
            )
            ->filtersFormColumns(3)
            ->actions([
                ViewAction::make()
                    ->modal()
                    ->modalHeading(__('View Library Stats'))
                    ->modalContent(fn ($record) => self::getInfolistContent($record))
                    ->slideOver(),
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
                // Add export excel button
                ExportBulkAction::make()
                ->icon('uiw-file-excel')
                ->color('success')
                ->label(__('global.export_excel_file'))
                ->exports([
                    ExcelExport::make('table')
                        ->fromTable()
                        //->only(['id', 'name', 'email', 'created_at', 'updated_at'])
                       // ->except(['document.Coverr'])
                        // ->modifyQueryUsing(fn ($query) => $query->orderBy('YEAR', 'asc'))
                        ->askForFilename(__('cruds.documentCopy.title'), __('global.please_give_file_name'))
                        ->withFilename(fn($filename) => __('cruds.documentCopy.title') . $filename),
                ])
                ->deselectRecordsAfterCompletion()
            //->visible(Auth::user()->can('banned_circulation::reader'))
            ,
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLibraryStats::route('/'),
            // 'create' => Pages\CreateLibraryStats::route('/create'),
            'view' => Pages\ViewLibraryStats::route('/{record}'),
            // 'edit' => Pages\EditLibraryStats::route('/{record}/edit'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('library.name')
                            ->label(__('cruds.librarystats.fields.code'))
                            ->weight(FontWeight::Bold)
                            ->size(TextEntry\TextEntrySize::Large),

                        Section::make(__('cruds.librarystats.fields.collection_stats'))
                            ->schema([
                                TextEntry::make('total_titles')
                                    ->label(__('cruds.librarystats.fields.total_titles'))
                                    ->icon('heroicon-o-book-open')
                                    ->numeric(
                                        decimalPlaces: 0,
                                        thousandsSeparator: ',',
                                    ),
                                TextEntry::make('total_copies')
                                    ->label(__('cruds.librarystats.fields.total_copies'))
                                    ->icon('heroicon-o-document-duplicate')
                                    ->numeric(
                                        decimalPlaces: 0,
                                        thousandsSeparator: ',',
                                    ),
                                TextEntry::make('total_copies_rfid_tagged')
                                    ->label(__('cruds.librarystats.fields.total_copies_rfid_tagged'))
                                    ->icon('heroicon-o-tag')
                                    ->numeric(
                                        decimalPlaces: 0,
                                        thousandsSeparator: ',',
                                    ),
                            ])
                            ->columns(3),

                        Section::make(__('cruds.librarystats.fields.reader_stats'))
                            ->schema([
                                TextEntry::make('total_readers')
                                    ->label(__('cruds.librarystats.fields.total_readers'))
                                    ->icon('heroicon-o-users')
                                    ->numeric(
                                        decimalPlaces: 0,
                                        thousandsSeparator: ',',
                                    ),
                                TextEntry::make('total_readers_female')
                                    ->label(__('cruds.librarystats.fields.total_readers_female'))
                                    ->icon('heroicon-o-user')
                                    ->numeric(
                                        decimalPlaces: 0,
                                        thousandsSeparator: ',',
                                    ),
                                TextEntry::make('total_readers_male')
                                    ->label(__('cruds.librarystats.fields.total_readers_male'))
                                    ->icon('heroicon-o-user')
                                    ->numeric(
                                        decimalPlaces: 0,
                                        thousandsSeparator: ',',
                                    ),
                            ])
                            ->columns(3),

                        TextEntry::make('other_stats')
                            ->label(__('cruds.librarystats.fields.other_stats'))
                            ->icon('heroicon-o-clipboard-document-list')
                            ->markdown(),
                    ]),

            ]);
    }
}
