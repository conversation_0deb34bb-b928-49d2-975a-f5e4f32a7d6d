<?php

use App\Http\Controllers\Api\BookController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ReaderController;
use App\Http\Controllers\Api\V1\BookController as BookControllerV1;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::post('/login', [AuthController::class, 'login']);

Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Route::post('/logout', [AuthController::class, 'logout']);

    Route::post('/readers/printed', [ReaderController::class, 'printedCard']);
    Route::post('/readers/renew', [ReaderController::class, 'renewCard']);

    Route::post('/readers/create', [ReaderController::class, 'createReaderAndUploadImage']);
    Route::post('/readers/upload-image', [ReaderController::class, 'uploadImage']);
    Route::get('/readers/{reader_id}/image', [ReaderController::class, 'getImageByReaderId']);
});

/*
|--------------------------------------------------------------------------
| Library Network API Routes List [Fennec]
|--------------------------------------------------------------------------
*/
// ---------------- BOOKS
Route::get('/books/{book}', [BookController::class, 'show']);
Route::get('/books', [BookController::class, 'index']);
Route::get('/readers/{reader_id}/img', [ReaderController::class, 'getImageByReaderId']);

// Route::get('/classifications', [BookController::class, 'classifications']);
// Route::get('/stats', [StatisticsController::class, 'index']);

// //---------------- READER
// Route::get('/reader/{reader}', [ReaderController::class, 'show']);
// Route::get('/readerimg/{reader}', [ReaderController::class, 'showphoto']);
