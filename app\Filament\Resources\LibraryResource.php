<?php

namespace App\Filament\Resources;

use App\Enums\LibraryStatus;
use App\Filament\Resources\LibraryResource\Pages;
use App\Mail\SyncDataNotWorking;
use App\Models\Library;
use App\Models\Municipality;
use App\Models\User;
use App\Models\Wilaya;
use Filament\Forms;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use PDF;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class LibraryResource extends Resource
{
    protected static ?string $model = Library::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?int $navigationSort = 0;

    public static function getModelLabel(): string
    {
        return __('cruds.location.title_singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('cruds.location.title');
    }

    public static function getNavigationLabel(): string
    {
        return __('cruds.library.title');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('cruds.libraryNetwork.title');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Label')
                    ->tabs([
                        Tab::make('infos')
                            ->label(__('cruds.library.infos'))
                            ->icon('heroicon-o-building-library')
                            ->schema([
                                TextInput::make('name')
                                    ->label(__('cruds.library.fields.name'))
                                    ->maxLength(255),
                                TextInput::make('minister')
                                    ->label(__('cruds.library.fields.minister'))
                                    ->default('وزارة الثقافة و الفنون')
                                    // ->helperText('وزارة الثقافة و الفنون')
                                    ->maxLength(255),
                                Select::make('wilaya_id')
                                    ->label(__('cruds.library.fields.wilaya'))
                                    ->relationship('wilaya', 'arabic_labale', fn (Builder $query) => $query->select(['id', 'arabic_labale', 'code']))
                                    ->getOptionLabelFromRecordUsing(fn ($record) => "{$record->code} - {$record->arabic_labale}")
                                    ->preload()
                                    ->reactive()
                                    ->afterStateUpdated(fn (callable $set) => $set('municipality_id', null))
                                    ->searchable(),
                                Select::make('municipality_id')
                                    ->label(__('cruds.library.fields.commune'))
                                    ->options(function (callable $get) {
                                        $wilaya = Wilaya::find($get('wilaya_id'));

                                        if (! $wilaya) {
                                            return Municipality::all()->pluck('arabic_labale', 'id')->toArray();
                                        }

                                        return $wilaya->municipalities->pluck('arabic_labale', 'id')->toArray();
                                    })
                                    ->preload()
                                    ->searchable(),
                                TextInput::make('code')
                                    ->label(__('cruds.library.fields.code'))
                                    ->maxLength(255),
                                Select::make('status')
                                    ->options(LibraryStatus::class)
                                    ->label(__('cruds.library.fields.status')),
                                TextInput::make('agent_name')
                                    ->label(__('cruds.library.fields.agent_name'))
                                    ->maxLength(255),
                            ]),
                        Tab::make('API')
                            ->label(__('cruds.library.api'))
                            ->icon('heroicon-o-cog-8-tooth')
                            ->schema([
                                TextInput::make('ip_adress')
                                    ->label(__('cruds.library.fields.ip_adress'))
                                    ->maxLength(255),
                                TextInput::make('api_port')
                                    ->label(__('cruds.library.fields.api_port'))
                                    ->maxLength(255),
                                TextInput::make('api_end')
                                    ->label(__('cruds.library.fields.api_end'))
                                    ->maxLength(255),
                                TextInput::make('books_end_point')
                                    ->label(__('cruds.library.fields.books_end_point'))
                                    ->maxLength(255),
                                TextInput::make('readers_end_point')
                                    ->label(__('cruds.library.fields.readers_end_point'))
                                    ->maxLength(255),
                                TextInput::make('stats_end_point')
                                    ->label(__('cruds.library.fields.stats_end_point'))
                                    ->maxLength(255),
                                TextInput::make('api_version')
                                    ->label(__('cruds.library.fields.api_version'))
                                    ->maxLength(255),

                                TextInput::make('software')
                                    ->label(__('cruds.library.fields.software'))
                                    ->maxLength(255),
                            ]),
                        Tab::make('contact')
                            ->label(__('cruds.library.contact'))
                            ->icon('heroicon-o-envelope')
                            ->schema([
                                TextInput::make('adress')
                                    ->label(__('cruds.library.fields.adress'))
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label(__('cruds.library.fields.phone'))
                                    ->tel()
                                    ->maxLength(255),
                                TextInput::make('fax')
                                    ->label(__('cruds.library.fields.fax'))
                                    ->maxLength(255),
                                TextInput::make('email')
                                    ->label(__('cruds.library.fields.email'))
                                    ->email()
                                    ->maxLength(255),
                                TextInput::make('website_url')
                                    ->label(__('cruds.library.fields.website_url'))
                                    ->maxLength(255),
                                TextInput::make('facebook_url')
                                    ->label(__('cruds.library.fields.facebook_url'))
                                    ->maxLength(255),
                                MarkdownEditor::make('notes')
                                    ->label(__('cruds.library.fields.notes'))
                                    ->columnSpanFull(),
                            ]),

                    ])->columnSpanFull()->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Tables\Columns\TextColumn::make('id')
                //     ->label(__('cruds.library.fields.id'))
                //     ->badge()
                //     ->searchable(),
                Tables\Columns\TextColumn::make('code')
                    ->label(__('cruds.library.fields.code'))
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('cruds.library.fields.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('minister')
                    ->label(__('cruds.library.fields.minister'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\SelectColumn::make('wilaya')
                    ->label(__('cruds.library.fields.wilaya'))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('commune')
                    ->label(__('cruds.library.fields.commune'))
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\ToggleColumn::make('status')
                    ->getStateUsing(fn (Library $record) => $record->status === 'Open' ? true : false)
                    ->onColor(fn ($record) => $record->status == 'Open' ? 'success' : 'gray')
                    ->offColor('gray')
                    ->onIcon(fn ($record) => $record->status == 'Open' ? 'heroicon-m-check' : 'heroicon-m-x-mark')
                    ->offIcon('heroicon-m-x-mark')
                    ->disabled(function ($record) {
                        return $record->ip_adress == '';
                    })
                    ->updateStateUsing(function (Library $r) {
                        $r->status === 'Closed'
                            ? $r->status = 'Open'
                            : $r->status = 'Closed';
                        $r->save();
                    }),
                Tables\Columns\TextColumn::make('agent_name')
                    ->label(__('cruds.library.fields.agent_name'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('ip_adress')
                    ->label(__('cruds.library.fields.ip_adress'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('api_port')
                    ->label(__('cruds.library.fields.api_port'))
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('api_end')
                    ->label(__('cruds.library.fields.api_end'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('books_end_point')
                    ->label(__('cruds.library.fields.books_end_point'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('readers_end_point')
                    ->label(__('cruds.library.fields.readers_end_point'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('stats_end_point')
                    ->label(__('cruds.library.fields.stats_end_point'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('api_version')
                    ->label(__('cruds.library.fields.api_version'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('software')
                    ->label(__('cruds.library.fields.software'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('adress')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('cruds.library.fields.adress'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('cruds.library.fields.phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('fax')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('cruds.library.fields.fax'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('cruds.library.fields.email'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('website_url')
                    ->label(__('cruds.library.fields.website_url'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('facebook_url')
                    ->label(__('cruds.library.fields.facebook_url'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('cruds.library.fields.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('cruds.library.fields.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('cruds.library.fields.deleted_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])->defaultSort('status', 'desc')
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
                // Tables\Actions\Action::make('ApiHealthCheck')
                //     ->label(__('global.api_health_check'))
                //     ->icon('heroicon-o-cog-8-tooth')
                //     ->color('info')
                //     ->button()
                //     ->action(function (Library $record) {
                //         //TODO: Send email notification to Library net admin
                //         $recipient = auth()->user();
                //         //*$recipientAdmins =  User::role('super_admin')->pluck('email')->toArray();
                //         $recipientAdmins =  User::role('super_admin')->get();
                //         // dd($recipient);
                //         try {
                //             $port = $record->api_port ?? '80';
                //             $endpoint = $record->books_end_point ?? '/api';
                //             if (filter_var($record->ip_adress, FILTER_VALIDATE_IP)) {
                //                 $url = "http://{$record->ip_adress}:{$port}{$endpoint}";
                //                 $response = Http::get($url);
                //                 if ($response->successful()) {
                //                     $recipientAdmins->each(function ($recipientAdmin) use ($recipient, $url) {
                //                         $recipientAdmin->notify(new ApiHealthCheckNotification($recipient, $url));
                //                     });
                //                     Notification::make()
                //                         ->title('API Health Check Notification Sent')
                //                         ->success()
                //                         ->send();
                //                 } else {
                //                     Notification::make()
                //                         ->title('API Health Check Failed')
                //                         ->error()
                //                         ->send();
                //                 }
                //             }
                //         } catch (\Exception $e) {
                //             Notification::make()
                //                 ->title('API Health Check Failed')
                //                 ->error()
                //                 ->send();
                //         }
                //     }),
                Tables\Actions\Action::make('ApiHealthCheckReport')
                    ->label(__('filament.api-health-check.print_selected'))
                    ->icon('heroicon-o-document-text')
                    ->color('danger')
                    ->button()
                    ->form([
                        Forms\Components\RichEditor::make('notes')
                            ->label(__('Additional Notes'))
                            // ->required()
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'strike',
                                'bulletList',
                                'orderedList',
                                'redo',
                                'undo',
                            ]),
                    ])
                    ->action(function (Library $record, array $data) {
                        $results = [];
                        $port = $record->api_port ?? '80';

                        // Check books endpoint
                        $booksEndpoint = $record->books_end_point ?? '/api/books';
                        $results['books'] = self::checkEndpoint($record->ip_adress, $port, $booksEndpoint);

                        // Check readers endpoint
                        $readersEndpoint = $record->readers_end_point ?? '/api/readers';
                        $results['readers'] = self::checkEndpoint($record->ip_adress, $port, $readersEndpoint);

                        // Check stats endpoint
                        $statsEndpoint = $record->stats_end_point ?? '/api/stats';
                        $results['stats'] = self::checkEndpoint($record->ip_adress, $port, $statsEndpoint);

                        // Check stats classifications
                        $classificationsEndpoint = '/api/classifications';
                        $results['classifications'] = self::checkEndpoint($record->ip_adress, $port, $classificationsEndpoint);

                        // Generate PDF report with proper data handling
                        $viewData = [
                            'library' => $record,
                            'results' => $results,
                            'timestamp' => now()->format('Y-m-d H:i:s'),
                           // 'user' => auth()?->user(),
                            'user' => Auth::user(),
                            'notes' => $data['notes'] ?? null,
                        ];

                        // Add automatic note if any endpoint check failed
                        $hasFailedEndpoints = false;
                        foreach ($results as $endpoint => $result) {
                            if ($result['status'] !== 'success') {
                                $hasFailedEndpoints = true;
                                break;
                            }
                        }

                        if ($hasFailedEndpoints) {
                            $autoNote = '<p style="color: #b91c1c; font-weight: bold;">ملاحظة هامة: يجب إصلاح نقاط النهاية التي فشلت في الاختبار في أقرب وقت ممكن لضمان استمرارية الخدمة.</p>';
                            $viewData['notes'] = $viewData['notes'] ? $viewData['notes'].$autoNote : $autoNote;
                        }

                        try {
                            $pdf = PDF::loadView('pdf.api-health-check-report', $viewData);
                            $pdf->autoScriptToLang = true;
                            $pdf->autoArabic = true;
                            $pdf->autoLangToFont = true;

                            return response()->streamDownload(
                                function () use ($pdf) {
                                    echo $pdf->output();
                                },
                                'api-health-check-'.$record->code.'-'.now()->format('Y-m-d').'.pdf',
                                [
                                    'Content-Type' => 'application/pdf',
                                ]
                            );
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('PDF Generation Failed')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();

                            return back();
                        }
                    }),
                Tables\Actions\Action::make('OpenUrl')
                    ->label(__('global.open'))
                    ->icon('heroicon-o-link')
                    ->color('success')
                    ->iconButton()
                    ->tooltip('Open Url In New Tab')
                    ->url(function (Library $record, $livewire) {
                        $port = $record->api_port ?? '80';
                        $endpoint = $record->books_end_point ?? '/api';
                        if (filter_var($record->ip_adress, FILTER_VALIDATE_IP)) {
                            return "http://$record->ip_adress:$port$endpoint";
                        }
                    })
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make(),
                // Tables\Actions\Action::make('FindBook')
                //     ->label(__('global.check'))
                //     ->icon('heroicon-o-cog-8-tooth')
                //     ->color('info')
                //     ->button()
                //     ->action(function (Library $record) {
                //         //TODO: Send email notification to Library net admin
                //         $recipient = auth()->user();
                //         //*$recipientAdmins =  User::role('super_admin')->pluck('email')->toArray();
                //         $recipientAdmins =  User::role('super_admin')->get();
                //         // dd($recipient);
                //         try {
                //             $port = $record->api_port ?? '80';
                //             if (filter_var($record->ip_adress, FILTER_VALIDATE_IP)) {
                //                 $baseApiUrl = "http://$record->ip_adress:$port/api/stats";
                //                 $response = Http::get($baseApiUrl);
                //             }
                //             Notification::make()
                //                 ->title('الرابط يعمل')
                //                 ->body($baseApiUrl)
                //                 ->date(now())
                //                 ->icon('heroicon-o-check-circle')
                //                 ->success()
                //                 // ->sendToDatabase($recipient);
                //                 ->sendToDatabase($recipientAdmins);
                //             Mail::to($recipient)
                //                 //->cc($moreUsers)
                //                 //->bcc($evenMoreUsers)
                //                 ->Send(new SyncDataNotWorking($record, $baseApiUrl));
                //             return $response->successful();
                //         } catch (\Exception $e) {
                //             Notification::make()
                //                 ->title('الرابط لا يعمل')
                //                 ->date(now())
                //                 ->body($e->getMessage())
                //                 ->danger()
                //                 //->sendToDatabase($recipient);
                //                 ->sendToDatabase($recipientAdmins);
                //             Mail::to($recipient)
                //                 //->cc($moreUsers)
                //                 // ->bcc($recipientAdmins)
                //                 ->Send(new SyncDataNotWorking($record, $e->getMessage()));
                //             return false;
                //         }
                //     }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    // /Tables\Actions\ForceDeleteBulkAction::make(),
                    // Tables\Actions\RestoreBulkAction::make(),
                ]),
                // Add export excel button
               ExportBulkAction::make()
                ->icon('uiw-file-excel')
                ->color('success')
                ->label(__('global.export_excel_file'))
                ->exports([
                    ExcelExport::make('table')
                        ->fromTable()
                        //->only(['id', 'name', 'email', 'created_at', 'updated_at'])
                       // ->except(['document.Coverr'])
                        // ->modifyQueryUsing(fn ($query) => $query->orderBy('YEAR', 'asc'))
                        ->askForFilename(__('cruds.documentCopy.title'), __('global.please_give_file_name'))
                        ->withFilename(fn($filename) => __('cruds.documentCopy.title') . $filename),
                ])
                ->deselectRecordsAfterCompletion()
            //->visible(Auth::user()->can('banned_circulation::reader'))
            ,
            ]);
    }

    protected static function checkEndpoint(string $ipAddress, string $port, string $endpoint): array
    {
        if (! filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            return [
                'status' => 'error',
                'code' => null,
                'message' => 'Invalid IP address',
                'response_time' => null,
                'timestamp' => now()->format('Y-m-d H:i:s'),
            ];
        }

        $url = "http://{$ipAddress}:{$port}{$endpoint}";

        try {
            $startTime = microtime(true);
            $response = Http::timeout(5)->get($url);
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2); // in milliseconds

            $message = $response->successful()
                ? 'Endpoint is accessible'
                : 'Error: '.$response->status().' - '.$response->body();

            return [
                'status' => $response->successful() ? 'success' : 'error',
                'code' => $response->status(),
                'message' => $message,
                'response_time' => $responseTime,
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'url' => $url,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'code' => null,
                'message' => 'Request failed: '.$e->getMessage(),
                'response_time' => null,
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'url' => $url,
            ];
        }
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLibraries::route('/'),
            'create' => Pages\CreateLibrary::route('/create'),
            'view' => Pages\ViewLibrary::route('/{record}'),
            'edit' => Pages\EditLibrary::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
