<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DocumentRecordCollection;
use App\Http\Resources\DocumentRecordResource;
use App\Models\DocumentRecord;
use Illuminate\Http\Request;

class BookController extends Controller
{
    public function show(DocumentRecord $book): DocumentRecordResource
    {
        return new DocumentRecordResource($book);
    }

    // 33099  // empty 2350  // without empty 30749 // not complete 635
    /**
     * @OA\Get(
     *     path="/api/books",
     *
     *     @OA\Response(response="200", description="Display a listing of Document Records.")
     * )
     */
    public function index(Request $request) // NoticeCollection
    {
        $filter_options = [
            'options' => [
                'min_range' => 1,
                'max_range' => 100,
            ],
        ];

        if (filter_var($request->get('limit'), FILTER_VALIDATE_INT, $filter_options) !== false) {
            $perPage = (int) ($request->get('limit'));
        } else {
            $perPage = 100;
        }

        // return new DocumentRecordCollection(
        //     DocumentRecord::Where('document_code', '<>', null)
        //         ->where('document_code', '<>', '')
        //         //->where('STA_ID', 4)
        //         ->paginate($perPage)
        // );

        // Initialize the query builder
        $query = DocumentRecord::query();

            $query->whereNotNull('document_code')->where('document_code', '<>', '');

        // Optional filters
        if ($request->has('document_title')) {
            $query->where('document_title', 'like', '%'.$request->input('document_title').'%');
        }

        if ($request->has('isbn')) {
            $query->where('isbn', $request->input('isbn'));
            // dd('here');
        }

        if ($request->has('keywords')) {
            $query->where('keywords', 'like', '%'.$request->input('keywords').'%');
        }

            return $query->paginate($perPage);
        });

        return new DocumentRecordCollection($documentRecords);
    }

    public function classifications()
    {
        // return ClassificationResource::collection(Classification::orderBy('CLA_ID')->get());
    }
}
