<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DocumentRecord;
use App\Http\Resources\DocumentRecordResource;
use App\Http\Resources\DocumentRecordCollection;
use App\Http\Resources\DocumentRecordResource;
use App\Models\Cataloging\DocumentRecord;

class BookController extends Controller
{
    public function index(): DocumentRecordCollection
    {
        return new DocumentRecord(DocumentRecordResource::paginate(10));
    }
}
