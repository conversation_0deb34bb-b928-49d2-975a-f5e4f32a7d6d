<?php

namespace App\Providers\Filament;

use App\Filament\Pages\BookSearchApiPage;
use App\Filament\Pages\Dashboard;
use App\Filament\Pages\DocumentDashboard;
use App\Filament\Pages\FennecBackup;
use App\Filament\Pages\ManageCirculationSettings;
use App\Filament\Pages\OpacFennec;
use App\Filament\Pages\ReadersDashboard;
use App\Filament\Pages\ReaderSearchApiPage;
use App\Filament\Pages\SystemDashboard;
use App\Filament\Widgets\ApiHealthStatus;
use App\Filament\Widgets\AlgeriaMapWidget;
use App\Filament\Widgets\StatsOverview;
use Awcodes\LightSwitch\Enums\Alignment;
use Awcodes\LightSwitch\LightSwitchPlugin;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Mvenghaus\FilamentScheduleMonitor\FilamentPlugin as CronJobMonitor;
use ShuvroRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackupPlugin;
use Filament\Support\Assets\Js;
use Filament\Support\Facades\FilamentAsset;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->plugins([

                LightSwitchPlugin::make()
                    ->position(Alignment::TopCenter),
                FilamentShieldPlugin::make()
                    ->gridColumns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 3,
                    ])
                    ->sectionColumnSpan(1)
                    ->checkboxListColumns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 3,
                    ])
                    ->resourceCheckboxListColumns([
                        'default' => 1,
                        'sm' => 2,
                    ]),
                FilamentSpatieLaravelBackupPlugin::make()
                    ->usingPolingInterval('30s')
                    ->usingPage(FennecBackup::class),
                CronJobMonitor::make(),
            ])

            ->login(\App\Filament\Auth\CustomLogin::class)
            ->profile()
            ->databaseNotifications()
            ->databaseNotificationsPolling('5s')
            ->colors([
                'primary' => Color::Orange,
            ])
            ->font('Tajawal')
            ->favicon(asset('images/ministerlogo.svg'))
            ->brandName('National Network of Principal Libraries Platform')
            ->brandLogo(fn () => view('platform'))
            ->brandLogoHeight('3rem')

            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Dashboard::class,
//                // OpacFennec::class,
                // DocumentDashboard::class,
                // ReadersDashboard::class,
                SystemDashboard::class,
                // ManageCirculationSettings::class,
                // BookSearchApiPage::class,
                // ReaderSearchApiPage::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                StatsOverview::class,
                ApiHealthStatus::class,
                // ReadersLibraryLog::class,
                // LatestReadesLoged::class,
                AlgeriaMapWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            // ->renderHook(
            //     'panels::body.start',
            //     fn() => view('fennec.layout.waves-bg'),
            // )
            // ->sidebarCollapsibleOnDesktop()
            ->sidebarFullyCollapsibleOnDesktop()
            ->navigationItems([
                //                NavigationItem::make('DocumentStatistics')
                //                    ->label(fn () => __('panel.pages.DocumentStatistics'))
                //                    ->url(fn (): string => StatsDocumentRecord::getUrl())
                //                    ->icon('heroicon-o-presentation-chart-line')
                //                    ->group(fn () => __('panel.groups.statistics'))
                //                    // ->visible(auth()->user()?->can('viewAny', DocumentRecord::class))
                //                    //->visible(auth()->user()?->hasRole('super_admin'))
                //                    ->sort(0),
                //                NavigationItem::make('ReaderStatistics')
                //                    ->label(fn () => __('panel.pages.ReadersStatistics'))
                //                    ->url(fn (): string => StatsReaders::getUrl())
                //                    ->icon('heroicon-o-presentation-chart-line')
                //                    ->group(fn () => __('panel.groups.statistics'))
                //                    ->sort(0),
            ])
            ->viteTheme('resources/css/filament/admin/theme.css');
    }

    public function boot(): void
    {
        // TODO: Register Lang Changer to Login Page
        //        FilamentView::registerRenderHook(
        //            'panels::auth.login.form.before',
        //            fn () => view('fennec.login-lang'),
        //        );

        //        Filament::serving(function (){
        //            Filament::registerUserMenuItems([
        //              'account' =>  MenuItem::make()
        //                ->label('Your Profile')
        //                ->url(UserResource::getUrl('edit',['record' => auth()->user()]))
        //            ]);
        //        });

        // FilamentAsset::register([
        //     Js::make('interactive-map', resource_path('js/interactive-map.js')),
        // ]);
        // The interactive-map.js is imported and handled by resources/js/app.js
    }
}
