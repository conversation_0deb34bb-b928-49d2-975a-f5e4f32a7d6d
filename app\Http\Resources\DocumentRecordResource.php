<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DocumentRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'library_id' => $this->library_id,
            'library_name' => $this->library->name,
            'library_code' => $this->library->code,
            'document_code' => $this->document_code,
            'document_title' => $this->document_title,
            'additional_title' => $this->additional_title,
            'parallel_title' => $this->parallel_title,
            'volume_title' => $this->group_title,
            'collection_title' => $this->collection_title,
            'collection_number' => $this->collection_number,
            // 'country' => CountryResource::collection($this->country),
            'document_type_id' => $this->document_type_id,
            'document_type_name' => $this->documentType->name ?? null,

            // 'classification' => ClassificationResource::collection($this->classification),

            'publication_year' => $this->publication_year,
            'publication_location' => $this->publication_place,

            // 'status' => new DocumentStatusResource($this->status),

            'illustration' => $this->illustration,
            'number_pages' => $this->number_units,
            'format' => $this->sizes,
            'isbn' => $this->isbn,
            'issn' => $this->issn,
            'other_number' => $this->other_number,

            'note' => $this->note,
            'resume' => $this->summary,

            'keywords' => $this->keywords,

            'library' => null,
            'langue' =>$this->language,
            // 'languages' => LanguageResource::collection($this->languages),
             'subject_headings' => $this->subjectHeading,

            'authors' => json_decode($this->authors, true),
             'editors' => json_decode($this->editors, true),

            'number_copies' => $this->number_copies,
             'copies' => $this->documentCopies,

            'book_cover' => $this->book_cover,

            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
        ];
    }
}
