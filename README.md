# BplpNet - Algerian National Library Network

## About
BplpNet is a comprehensive Laravel-based web application that serves as a centralized network system for synchronizing and managing libraries across Algeria. Built with Filament PHP admin panel, it facilitates seamless data exchange, resource sharing, and standardized management between all connected libraries in the country.

## Core Features
- **Library Synchronization**: Automated synchronization of library catalogs, collections, and resources across the national network
- **Centralized Catalog Management**: Unified catalog system for all participating libraries
- **Multi-library Resource Sharing**: Facilitates inter-library loans and resource sharing
- **User Management System**: Comprehensive user roles and permissions for librarians, administrators, and patrons
- **Multilingual Support**: Interface available in multiple languages including Arabic, French, and English
- **Advanced Search Capabilities**: Search across all connected libraries' collections
- **Collection Analytics**: Statistical analysis and reporting of library collections and usage
- **Digital Resource Management**: Support for digital media and e-resources
- **Automated Synchronization Jobs**: Console commands for scheduled data synchronization

## Technical Features
- Laravel Framework 12 ++ Implementation with Filament PHP Admin Panel latest
- was on MySQL Andmigrated to PostgreSQL Database Integration for robust data storage
- Queue System Support for background processing of synchronization tasks
- Advanced Session Management for user interactions
- Email Notification System for alerts and communications
- Redis Support for caching and performance optimization
- AWS Integration Capability for cloud storage solutions
- Pusher Integration for Real-time Updates and Notifications
- Vite for Modern Frontend Asset Building

## Admin Panel Resources
The BplpNet admin panel is built using Filament PHP and includes the following resources:

- **Book Resource**: Manages the central catalog of books across all libraries, including metadata, availability, and location information.
- **Library Resource**: Manages information about participating libraries including contact details, location, operating hours, and API connection settings.
- **User Resource**: Handles user management with different permission levels for system administrators, librarians, and staff members.
- **LibraryStats Resource**: Displays and manages statistical data synchronized from all libraries, including reader counts, collection sizes, and usage metrics.
- **Author Resource**: Manages author information for the book catalog, ensuring consistent metadata across the network.
- **Category Resource**: Organizes books into categories and subject areas for better searchability.
- **Publisher Resource**: Maintains publisher information for all books in the system.
- **Loan Resource**: Tracks book loans, returns, and inter-library lending activities.
- **Reader Resource**: Manages reader/patron information and their borrowing privileges across the network.

Each resource in the admin panel provides comprehensive CRUD operations, filtering capabilities, and relationship management to ensure efficient administration of the national library network.

## Environment Setup
- Configured for local development environment
- Debug mode enabled for development
- Uses PostgreSQL as the primary database
- Supports various caching and queue drivers
- Configured for SMTP mail sending

## Todo List
- [ ] Develop an API Health Check Tool that:
  - Validates library API endpoints from just the IP address
  - Tests all critical endpoints (e.g., `/api/stats`, `/api/books`, `/api/readers`)
  - Generates comprehensive health reports for each library
  - Implements automatic notification for non-responsive endpoints
  - Provides a dashboard view of API status across the network
  - Allows manual triggering of checks from the admin panel
  - Stores historical connectivity data for troubleshooting
  - Includes timeout and retry logic for intermittent connections
- [ ] API Health Check PDF Reports
  - Implement functionality to generate full PDF reports for API Health Checks
  - Features:
    - Generate reports for selected day or date range
    - Include custom heading and footer
    - Make reports printable and mailable
    - Include status summary and detailed health check information
    - Allow filtering by library and status
    - Support for both individual and bulk report generation
  - Implementation steps:
    1. Create PDF generation service using a library like DomPDF or Snappy PDF
    2. Design report templates with customizable headers/footers
    3. Add routes for PDF generation
    4. Implement email functionality to send reports
    5. Add date picker for selecting report timeframe
- [ ] Configure database connection details for production
- [ ] Set up proper mail configuration for production
- [ ] Enhance user authentication system with library-specific roles
- [ ] Complete remaining database migrations and seeders
- [ ] Develop additional frontend components with Vite
- [ ] Implement comprehensive logging for synchronization activities
- [ ] Expand API endpoints for external library systems integration
- [ ] Set up real-time notifications for synchronization events
- [ ] Write comprehensive tests for synchronization processes
- [ ] Optimize synchronization algorithms for production deployment
- [ ] Create detailed documentation for API endpoints and synchronization commands
- [ ] Implement advanced security measures for sensitive library data
- [ ] Complete multilingual support for all interface elements

## Future Enhancements
- Mobile application for librarians and patrons
- Advanced reporting and analytics dashboard
- Enhanced user role management system with granular permissions
- Integration with international library classification systems
- Performance optimization for large-scale catalog operations
- Expanded localization support for additional Algerian dialects
- Digital preservation features for rare and historical documents
- Machine learning recommendations for related resources
- Public-facing portal for nationwide library resource discovery

## Documentation Plan
- User Manuals (Arabic, French, English)
- Administrator Guides
- API Documentation
- Synchronization Process Documentation
- Deployment Guides
- Contributing Guidelines
- Troubleshooting Documentation

## Installation

1. Clone the repository
```bash
git clone [repository-url]
cd BplpNet
```
## Synchronization Commands

The following console commands are available for library data synchronization:

```bash
php artisan sync:books    # Sync books data from multiple APIs daily at midnight
php artisan sync:stats    # Sync Stats from multiple APIs daily at midnight
```

### Command Options
The sync:stats command supports an optional library ID parameter to sync data from a specific library:

```bash
php artisan sync:stats            # Sync stats from all libraries
php artisan sync:stats {library_id}  # Sync stats from a specific library
 
```

This flexibility allows administrators to target specific libraries for synchronization when needed, which is useful for troubleshooting or updating data from a particular location.
