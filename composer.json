{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "awcodes/filament-table-repeater": "^3.1", "awcodes/light-switch": "^1.0", "bezhansalleh/filament-language-switch": "^3.0", "bezhansalleh/filament-shield": "^3.2", "carlos-meneses/laravel-mpdf": "^2.1", "codeat3/blade-uiw-icons": "^1.6", "dotswan/filament-laravel-pulse": "^1.1", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.3", "filament/spatie-laravel-settings-plugin": "^3.2", "filament/tables": "^3.1", "flowframe/laravel-trend": "^0.4.0", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^12.0", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "livewire/livewire": "^3.0", "livewire/volt": "^1.0", "log1x/laravel-webfonts": "^2.0", "mvenghaus/filament-plugin-schedule-monitor": "^3.0", "psr/simple-cache": "^3.0", "pxlrbt/filament-excel": "^2.1", "shuvroroy/filament-spatie-laravel-backup": "^2.0", "simplesoftwareio/simple-qrcode": "~4", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-medialibrary": "^11.0", "spatie/laravel-schedule-monitor": "^3.8", "wildside/userstamps": "^2.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "laravel/telescope": "^5.5", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}