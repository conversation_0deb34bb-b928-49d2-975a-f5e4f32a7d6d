<?php

namespace App\Console\Commands;

use App\Models\Library;
use App\Models\LibraryStats;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SyncStatsDaily extends Command
{
    protected $signature = 'sync:stats';

    protected $description = 'Sync Stats from multiple APIs daily at midnight';

    private function generateBaseApiUrlsFromLibrariesTable()
    {

        $baseApiUrls = [];
        $libraries = Library::where('status', 'Open')->get();

        foreach ($libraries as $library) {

            $libraryId = $library->id;
            $libraryIp = $library->ip_adress;
            $port = $library->api_port ?? '80';
            if (filter_var($libraryIp, FILTER_VALIDATE_IP)) {
                $baseApiUrl = "http://$libraryIp:$port/api";
            }

            $baseApiUrls[$libraryId] = $baseApiUrl;
        }

        return $baseApiUrls;
    }

    public function handle()
    {
        $recipient = User::all();

        // dd($this->generateBaseApiUrlsFromLibrariesTable());
        $baseApiUrls = $this->generateBaseApiUrlsFromLibrariesTable();
        // $baseApiUrls = [
        //     //'4' => 'http://**************:45380/api',
        //     '5' => 'http://*************:54380/api',
        // ];
        //  [
        //     '1' => 'http://*************:45380/api',
        //     '2' => 'http://*************/api',
        //     '3' => 'http://**************:45380/api',

        // ];
        // dd($baseApiUrls);

        $limit = 10;
        $scraped_records = 0;

        foreach ($baseApiUrls as $key => $base_url) {
            $books_url = $base_url.'/books';
            // $library = Library::where('code', $books->bplp_info->library_code)->first()
            $this->info('Library : '.$base_url);
            if ($this->isApiAvailable($books_url)) {
                $response = Http::get($books_url, [
                    'limit' => $limit,
                ]);
                $books = json_decode($response->body());
                $library = Library::where('code', $books->bplp_info->library_code)->first();
                $library_code = $library->id;

                // adding stats if available
                if ($this->isApiAvailable($base_url.'/stats')) {
                    $this->info('Stats URL : '.$base_url.'/stats');
                    $response_stats = Http::get($base_url.'/stats');
                    $stast = json_decode($response_stats->body());
                    $this->updateOrCreateLibraryStats($stast, $library_code);
                }
            } else {
                // TODO: Send notification To bplp email
                // TODO: Send notification to admin with database notifications
                Notification::make()
                    ->title('الرابط  لا يعمل')
                    ->body($base_url)
                    ->date(now())
                    // ->icon('heroicon-o-check-circle')
                    ->danger()
                    ->sendToDatabase($recipient);
                // TODO:Send Email TO Admin/Dev and bplp having the problem

                //                Notification::make()
                //                    ->title('Library Skipped')
                //                    ->info()
                //                    ->body("API at {$base_url} is not available. Skipping to the next API.")
                //                    ->send();
                // TODO: Update the bplp status to Closed in database
                Log::warning("API at {$base_url} is not available. Skipping to the next API.");
            }
        }
        // Log the result or send notifications if needed
        Notification::make()
            ->title('Synced stats from multiple Libraries APIs')
            ->success()
            ->body('Synced stats from multiple APIs. Total records: '.$scraped_records)
            ->sendToDatabase($recipient);
        Log::info('Synced stats from multiple APIs. Total records: '.$scraped_records);
    }

    private function isApiAvailable($url)
    {
        try {
            $response = Http::get($url);

            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }

    private function updateOrCreateLibraryStats($stast, $library_code)
    {
        LibraryStats::updateOrCreate(
            // conditions
            [
                'library_id' => $library_code,
            ],
            // inserting fields
            [
                'library_id' => $library_code,
                'total_readers' => $stast->total_readers ?? '',
                'total_readers_female' => $stast->total_readers_female ?? '',
                'total_readers_male' => $stast->total_readers_male ?? '',
                'total_titles' => $stast->total_titles,
                'total_copies' => $stast->total_exmplaires,
                'total_copies_rfid_tagged' => $stast->total_exmplaires_rfid_tagged ?? '',
                'other_stats' => json_encode($stast->other_stats ?? ''),
            ]
        );
    }
}
