<?php

namespace App\Livewire;

use App\Models\Library;
use Livewire\Component;
use Illuminate\Support\Collection; // Add this if not auto-imported

class InteractiveMap extends Component
{
    public Collection $libraries;
    public ?Library $hoveredLibrary = null;
    public array $libraryDetails = []; // For quick lookup in Blade

    // Colors
    public string $colorOpen = '#22c55e'; // Green
    public string $colorClosed = '#ff6060'; // Red
    public string $defaultSvgFill = '#7c7c7c'; // Original SVG fill

    public function mount()
    {
        $this->libraries = Library::with('wilaya')->get(); // Eager load wilaya if you need its name
       // dd($this->libraries);
        foreach ($this->libraries as $library) {
            $this->libraryDetails[$library->code] = [
                'name' => $library->name,
                'code' => $library->code,
                'status' => $library->status === 'Open' ? __('widgets.open') : __('widgets.closed'),
                'wilaya' => $library->wilaya->name,
                'status_color' => $library->isOpen() ? $this->colorOpen : $this->colorClosed,
                'link' => route('library.show', ['library' => $library]) // Pass the model instance
            ];
        }
    }

    public function showInfo(string $code)
    {
        $this->hoveredLibrary = $this->libraries->firstWhere('code', $code);
    }

    public function hideInfo()
    {
        $this->hoveredLibrary = null;
    }

    public function getRegionFillColor(string $regionCode): string
    {
        if (isset($this->libraryDetails[$regionCode])) {
            return $this->libraryDetails[$regionCode]['status_color'];
        }
        return $this->defaultSvgFill; // Fallback to default SVG color
    }

    public function render()
    {
        return view('livewire.interactive-map');
    }
}