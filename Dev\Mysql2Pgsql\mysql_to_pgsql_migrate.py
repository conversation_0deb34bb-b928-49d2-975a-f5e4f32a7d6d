import mysql.connector
import psycopg2
from psycopg2.extras import execute_values
import logging
from datetime import datetime
import sys
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

# PostgreSQL reserved words that might require quoting if used as identifiers
# This list should be consistent with how create_pgsql_table quotes.
PG_RESERVED_WORDS_FOR_QUOTING = [
    'group', 'order', 'user', 'comment', 'check', 'constraint', 'default', 'desc', 'false',
    'for', 'foreign', 'from', 'grant', 'primary', 'references', 'true', 'unique', 'with'
    # Add other relevant keywords if necessary, but this matches the original script's implied list.
]

# Database configurations
MYSQL_CONFIG = {
    'host': '*************',
    'user': 'root',  # Update with your MySQL username
    'password': '',  # Update with your MySQL password
    'database': 'bplpnetwork'  # Update with your MySQL database name
}

PGSQL_CONFIG = {
    'host': 'localhost',
    'database': 'mcabplpnet',
    'user': 'BCTS',
    'password': ''  # No password as specified, update if needed
}

def get_mysql_tables(cursor):
    """Get all tables from MySQL database."""
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    # Decode bytearray table names to strings
    decoded_tables = [table[0].decode('utf-8') if isinstance(table[0], (bytes, bytearray)) else table[0] for table in tables]
    # Debug logging
    logging.info(f"Found {len(decoded_tables)} tables in MySQL database")
    logging.info(f"Table names: {decoded_tables}")
    return decoded_tables

def get_table_structure(cursor, table_name):
    """Get table structure from MySQL."""
    cursor.execute(f"DESCRIBE `{table_name}`")
    structure = cursor.fetchall()
    # Decode bytearray column names and types
    decoded_structure = []
    for column in structure:
        decoded_column = []
        for item in column:
            if isinstance(item, (bytes, bytearray)):
                decoded_column.append(item.decode('utf-8'))
            else:
                decoded_column.append(item)
        decoded_structure.append(tuple(decoded_column))
    return decoded_structure

def create_pgsql_table(pg_cursor, table_name, structure):
    """Create table in PostgreSQL based on MySQL structure."""
    columns_def = []
    primary_keys = []

    for column_data in structure:
        name = column_data[0]  # Already decoded in get_table_structure
        type_info = column_data[1]  # Already decoded in get_table_structure
        null_allowed = column_data[2] == "YES"
        key_info = column_data[3]
        default_value = column_data[4] if column_data[4] else None
        extra = column_data[5]  # Already decoded in get_table_structure

        # Handle reserved words for column names
        pg_col_name = f'"{name}"' if name.lower() in PG_RESERVED_WORDS_FOR_QUOTING else name

        # Type conversion from MySQL to PostgreSQL
        pg_type = ""
        is_serial = False

        if "auto_increment" in extra.lower():
            if "bigint" in type_info.lower():
                pg_type = "BIGSERIAL"
            else:
                pg_type = "SERIAL"
            is_serial = True
        elif "tinyint(1)" == type_info.lower():
            pg_type = "BOOLEAN"
        elif "int" in type_info.lower():
            if "bigint" in type_info.lower():
                pg_type = "BIGINT"
            elif "mediumint" in type_info.lower():
                pg_type = "INTEGER"
            elif "smallint" in type_info.lower():
                pg_type = "SMALLINT"
            elif "tinyint" in type_info.lower():
                pg_type = "SMALLINT"
            else:
                pg_type = "INTEGER"
        elif "varchar" in type_info.lower():
            try:
                size = type_info.split("(")[1].split(")")[0]
                pg_type = f"VARCHAR({size})"
            except IndexError:
                pg_type = "TEXT"
        elif "char" in type_info.lower():
            try:
                size = type_info.split("(")[1].split(")")[0]
                pg_type = f"CHAR({size})"
            except IndexError:
                pg_type = "CHAR(1)"
        elif "text" in type_info.lower():
            pg_type = "TEXT"
        elif "datetime" in type_info.lower():
            pg_type = "TIMESTAMP"
        elif "timestamp" in type_info.lower():
            pg_type = "TIMESTAMP"
        elif "date" == type_info.lower():
            pg_type = "DATE"
        elif "time" == type_info.lower():
            pg_type = "TIME"
        elif "year" in type_info.lower():
            pg_type = "INTEGER"
        elif "enum" in type_info.lower() or "set" in type_info.lower():
            pg_type = "TEXT"
            logging.warning(f"Table {table_name}, column {name}: ENUM/SET type converted to TEXT. Manual review/creation of PG ENUM type might be needed.")
        elif "json" in type_info.lower():
            pg_type = "JSONB"
        elif "binary" in type_info.lower() or "blob" in type_info.lower():
            pg_type = "BYTEA"
        elif "decimal" in type_info.lower() or "numeric" in type_info.lower():
            try:
                precision, scale = type_info.split("(")[1].split(")")[0].split(',')
                pg_type = f"NUMERIC({precision.strip()},{scale.strip()})"
            except:
                pg_type = "NUMERIC"
        elif "float" in type_info.lower():
            pg_type = "REAL"
        elif "double" in type_info.lower():
            pg_type = "DOUBLE PRECISION"
        else:
            pg_type = type_info.upper()
            logging.warning(f"Table {table_name}, column {name}: Unknown MySQL type '{type_info}' mapped to '{pg_type}'. Review needed.")

        column_definition = f"{pg_col_name} {pg_type}"

        if not null_allowed and not is_serial:
            column_definition += " NOT NULL"

        if default_value is not None and not is_serial:
            if pg_type == "BOOLEAN":
                # Convert MySQL's 0/1 to PostgreSQL's false/true
                if default_value == "0":
                    pg_default = "DEFAULT false"
                elif default_value == "1":
                    pg_default = "DEFAULT true"
                else:
                    pg_default = f"DEFAULT {default_value}"
            elif pg_type in ["TEXT", "VARCHAR", "CHAR", "DATE", "TIMESTAMP", "TIME"] and not default_value.upper().startswith(("CURRENT_TIMESTAMP", "NOW()")):
                pg_default = f"DEFAULT '{default_value.replace("'", "''")}'"
            elif default_value.upper() in ("CURRENT_TIMESTAMP", "CURRENT_TIMESTAMP()"):
                pg_default = "DEFAULT CURRENT_TIMESTAMP"
            else:
                pg_default = f"DEFAULT {default_value}"
            column_definition += f" {pg_default}"

        columns_def.append(column_definition)

        if key_info == "PRI":
            primary_keys.append(pg_col_name)

    create_table_sql = f'CREATE TABLE IF NOT EXISTS "{table_name}" ({", ".join(columns_def)}'
    if primary_keys:
        create_table_sql += f', PRIMARY KEY ({", ".join(primary_keys)})'
    create_table_sql += ")"

    logging.info(f"Creating table {table_name} with SQL: {create_table_sql}")
    pg_cursor.execute(create_table_sql)

def get_pgsql_column_types(pg_cursor, table_name: str) -> dict:
    """Fetch column names and their data types for a PostgreSQL table."""
    query = """
    SELECT column_name, data_type
    FROM information_schema.columns
    WHERE table_schema = current_schema() AND table_name = %s;
    """
    # Try with original case, then lowercase if not found (PG often defaults to lowercase for unquoted identifiers)
    pg_cursor.execute(query, (table_name,))
    results = pg_cursor.fetchall()
    if not results and table_name != table_name.lower():
        pg_cursor.execute(query, (table_name.lower(),))
        results = pg_cursor.fetchall()

    if not results:
        logging.warning(f"Could not retrieve column types for PostgreSQL table '{table_name}'. Data type conversion might be impaired.")
        return {}
    return {row[0]: row[1] for row in results}

def migrate_data(mysql_cursor, pg_cursor, table_name, mysql_table_structure):
    """Migrate data from MySQL to PostgreSQL."""
    try:
        # Get data from MySQL
        mysql_cursor.execute(f"SELECT * FROM `{table_name}`")
        rows = mysql_cursor.fetchall()

        if not rows:
            logging.info(f"No data to migrate for table {table_name}")
            return

        # Get MySQL column names from the result set description for data mapping
        mysql_columns_from_desc = []
        for desc_col in mysql_cursor.description:
            col_name = desc_col[0]
            if isinstance(col_name, (bytes, bytearray)):
                mysql_columns_from_desc.append(col_name.decode('utf-8'))
            else:
                mysql_columns_from_desc.append(col_name)

        # Get PostgreSQL column types for accurate data conversion
        pg_column_info = get_pgsql_column_types(pg_cursor, table_name)

        # Prepare column names for the INSERT statement, quoting as necessary based on how they were created
        pg_insert_column_names = []
        for col_struct_data in mysql_table_structure:
            original_mysql_name = col_struct_data[0]
            # This logic should exactly match how pg_col_name is determined in create_pgsql_table
            pg_col_name_for_insert = f'"{original_mysql_name}"' if original_mysql_name.lower() in PG_RESERVED_WORDS_FOR_QUOTING else original_mysql_name
            pg_insert_column_names.append(pg_col_name_for_insert)

        columns_str = ", ".join(pg_insert_column_names)
        insert_sql = f'INSERT INTO "{table_name}" ({columns_str}) VALUES %s'

        processed_rows = []
        for row_idx, row_data in enumerate(rows):
            if len(row_data) != len(mysql_columns_from_desc):
                logging.error(f"Table {table_name}, row {row_idx}: Mismatch between data length ({len(row_data)}) and column count ({len(mysql_columns_from_desc)}). Skipping row.")
                continue

            processed_row_tuple = []
            for i, item in enumerate(row_data):
                original_mysql_col_name = mysql_table_structure[i][0] # Get original name from structure for consistent lookup

                # Determine the PG column type. Check original name, then lowercase.
                pg_col_type = pg_column_info.get(original_mysql_col_name)
                if pg_col_type is None and original_mysql_col_name != original_mysql_col_name.lower():
                    pg_col_type = pg_column_info.get(original_mysql_col_name.lower())

                current_value = None
                if item is None:
                    current_value = None
                elif isinstance(item, (bytes, bytearray)):
                    if pg_col_type == 'bytea':
                        current_value = memoryview(item)
                    else:
                        try:
                            # Attempt to decode using UTF-8. If this fails, it might be actual binary
                            # data not intended for a bytea column, or wrong encoding.
                            decoded_str = item.decode('utf-8')

                            if not decoded_str and pg_col_type not in ('text', 'character varying', 'character', 'json', 'jsonb'):
                                # For most non-textual types, an empty string from MySQL raw read should become NULL
                                current_value = None
                            elif pg_col_type in ('integer', 'bigint', 'smallint', 'serial', 'bigserial'):
                                current_value = int(decoded_str)
                            elif pg_col_type in ('numeric', 'decimal', 'real', 'double precision'):
                                current_value = float(decoded_str)
                            elif pg_col_type == 'boolean':
                                if decoded_str == '1': current_value = True
                                elif decoded_str == '0': current_value = False
                                else: current_value = None # Or raise error/log warning for invalid boolean
                            elif pg_col_type in ('date', 'timestamp without time zone', 'timestamp with time zone', 'time without time zone', 'time with time zone') and not decoded_str:
                                current_value = None # Empty string for date/time becomes NULL
                            else: # Includes text, varchar, char, json, jsonb, and valid date/time strings
                                current_value = decoded_str
                        except UnicodeDecodeError:
                            logging.error(f"Table {table_name}, column '{original_mysql_col_name}' (PG type {pg_col_type}): UTF-8 decode failed for value {item[:30]}... Using NULL.")
                            current_value = None
                        except ValueError as ve: # Handles int(''), float(''), etc.
                            logging.error(f"Table {table_name}, column '{original_mysql_col_name}' (PG type {pg_col_type}): ValueError '{str(ve)}' converting '{decoded_str if 'decoded_str' in locals() else item[:30]}'. Using NULL.")
                            current_value = None
                else: # Item is not None and not bytes/bytearray (e.g., already a Python type)
                    current_value = item

                processed_row_tuple.append(current_value)
            processed_rows.append(tuple(processed_row_tuple))

        if processed_rows:
            execute_values(pg_cursor, insert_sql, processed_rows, template=None, page_size=100)
            logging.info(f"Successfully migrated {len(processed_rows)} rows for table {table_name}")
        elif rows: # Rows were fetched but none were processed (e.g. all had errors)
             logging.warning(f"Fetched {len(rows)} rows for table {table_name}, but 0 rows were processed for insertion due to errors or mismatches.")

    except psycopg2.Error as pg_err:
        logging.error(f"PostgreSQL error migrating data for table {table_name}: {pg_err}")
        detailed_error = getattr(pg_err, 'diag', None)
        if detailed_error and hasattr(detailed_error, 'message_detail'):
            logging.error(f"Detail: {detailed_error.message_detail}")
        if hasattr(pg_cursor, 'query') and pg_cursor.query:
             logging.error(f"Failed SQL (approximate): {pg_cursor.query}")
        raise
    except Exception as e:
        logging.error(f"Generic error migrating data for table {table_name}: {str(e)}")
        if 'pg_cursor' in locals() and hasattr(pg_cursor, 'query') and pg_cursor.query:
            logging.error(f"Last PG query attempt (approximate): {pg_cursor.query}")
        raise

def main():
    try:
        # Connect to MySQL
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        mysql_cursor = mysql_conn.cursor(raw=True) # Use raw=True to get bytes for binary types

        # Connect to PostgreSQL
        pg_conn = psycopg2.connect(**PGSQL_CONFIG)
        pg_cursor = pg_conn.cursor()

        # Get all tables
        tables = get_mysql_tables(mysql_cursor)

        if not tables:
            logging.error("No tables found in MySQL database. Please check your database connection and permissions.")
            return

        # Migrate each table
        for table in tables:
            try:
                logging.info(f"Starting migration for table: {table}")

                # Get table structure
                structure = get_table_structure(mysql_cursor, table)

                # Create table in PostgreSQL
                create_pgsql_table(pg_cursor, table, structure)

                # Migrate data
                migrate_data(mysql_cursor, pg_cursor, table, structure)

                # Commit after each table
                pg_conn.commit()

                logging.info(f"Successfully completed migration for table: {table}")

            except Exception as e:
                logging.error(f"Error processing table {table}: {str(e)}")
                pg_conn.rollback() # Rollback changes for the current failed table
                continue # Continue with the next table

    except Exception as e:
        logging.error(f"Migration failed: {str(e)}")
        # If pg_conn exists and an error occurred before the loop or in connection phase
        if 'pg_conn' in locals() and pg_conn:
            pg_conn.rollback()
        # raise # Re-raise the exception if you want the script to exit with an error code
    finally:
        # Close connections
        if 'mysql_cursor' in locals() and mysql_cursor:
            mysql_cursor.close()
        if 'mysql_conn' in locals() and mysql_conn:
            mysql_conn.close()
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()
        if 'pg_conn' in locals() and pg_conn:
            pg_conn.close()

if __name__ == "__main__":
    main()
