<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\URL;

class DocumentRecordCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return parent::toArray($request);
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function with($request)
    {
        return [
            'bplp_info' => [
                // 'library_code' => env('LIBRARY_CODE', '0101'),
                'library_software' => env('LIBRARY_SOFTWARE', 'Fennec Samrt ILS'),
                // 'library_name' => env('LIBRARY_NAME', 'المكتبة الرئيسية للمطالعة العمومية أدرار'),
                // 'classification_method' => env('CLASSIFICATION_METHOD', 'Dewey Decimal Classification System'),
                // 'classification_plan_url' => URL::to('/api/classifications'),
            ],
        ];
    }
}
