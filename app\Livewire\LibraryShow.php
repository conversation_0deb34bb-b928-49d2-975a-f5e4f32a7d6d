<?php

namespace App\Livewire;

use App\Models\Library;
use Livewire\Component;

class LibraryShow extends Component
{
    public Library $library;

    public function mount($library) // Livewire can resolve the model by its code/ID if route model binding is set up
    {
        // If $library is a string (code), fetch it.
        // If <PERSON><PERSON>'s route model binding is working correctly (e.g. using 'code' as route key in Library model),
        // $library might already be a Library instance.
        if (is_string($library)) {
            $this->library = Library::where('code', $library)->firstOrFail();
        } elseif ($library instanceof Library) {
            $this->library = $library;
        } else {
            abort(404);
        }
    }

    public function render()
    {
        return view('livewire.library-show')
            ; // Assuming you have a layout
    }
}
