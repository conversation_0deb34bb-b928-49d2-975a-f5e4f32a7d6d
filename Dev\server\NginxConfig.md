### NginX site config

```bash

 # Enable gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 256;
    gzip_comp_level 5;
    gzip_vary on;
    gzip_proxied any;
    gzip_buffers 16 8k;
    
```

sudo certbot --nginx -d fennec.loc.bplpadrar.dz
```bash
sudo chown -R $USER:www-data storage
sudo chown -R $USER:www-data bootstrap/cache

chmod -R 775 storage
chmod -R 775 bootstrap/cache

sudo chown -R $USER:www-data /srv/web

```
CREATE DATABASE mcabplpnet OWNER bplpnetadmin;
GRANT ALL PRIVILEGES ON DATABASE mcabplpnet TO bplpnetadmin;


pg_restore -U bplpnetadmin -d mcabplpnet -v ~/todeploybplpnetwork.sql


sudo nano /etc/postgresql/17/main/pg_hba.conf

ALTER USER bplpnetadmin WITH PASSWORD 'BplpNet@2410';
